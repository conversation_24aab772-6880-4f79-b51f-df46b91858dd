# HTML App Manager - Troubleshooting Guide

## Common Issues and Solutions

### 1. Critical Error on Website
**Symptoms:** "There has been a critical error on this website" message

**Causes & Solutions:**
- **PHP Syntax Error:** Check WordPress error logs in `/wp-content/debug.log`
- **Missing PHP Extension:** Ensure ZipArchive extension is installed
- **Memory Limit:** Increase PHP memory limit in wp-config.php or .htaccess
- **File Permissions:** Ensure wp-content/uploads directory is writable

**Quick Fix:**
1. Enable WordPress debugging by adding to wp-config.php:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```
2. Check `/wp-content/debug.log` for specific error messages

### 2. ZipArchive Not Available
**Error:** "ZipArchive PHP extension is not installed"

**Solution for XAMPP:**
1. Open `php.ini` file (usually in `C:\xampp\php\php.ini`)
2. Find the line `;extension=zip` and remove the semicolon: `extension=zip`
3. Restart Apache server
4. Verify by creating a PHP file with `<?php phpinfo(); ?>` and check for ZIP support

### 3. File Upload Issues
**Symptoms:** Upload fails or times out

**Solutions:**
- Increase upload limits in php.ini:
```ini
upload_max_filesize = 50M
post_max_size = 50M
max_execution_time = 300
max_input_time = 300
memory_limit = 256M
```
- Restart Apache after changes

### 4. App Files Not Found Error
**Symptoms:** "App files not found" message when accessing subdomain

**Causes & Solutions:**
- **File Extraction Failed:** Check if files were properly extracted to `/wp-content/uploads/html-apps/`
- **Permissions:** Ensure the uploads directory is writable (755 or 775)
- **Path Issues:** Check if the file paths in database match actual file locations

### 5. Assets Not Loading (CSS, JS, Images)
**Symptoms:** App loads but styling/scripts/images are missing

**Solutions:**
- **Subdomain Configuration:** Ensure wildcard subdomain is properly configured
- **File Paths:** Check if asset paths in HTML are relative (not absolute)
- **MIME Types:** Verify server serves correct MIME types for assets

### 6. Subdomain Not Working
**Symptoms:** Subdomain shows default WordPress site or 404 error

**Solutions:**
- **DNS Configuration:** Add wildcard DNS record: `*.yourdomain.com`
- **Apache Configuration:** Add wildcard virtual host or ServerAlias
- **WordPress Multisite:** If using multisite, ensure subdomain mapping is correct

## Debugging Steps

### Step 1: Check PHP Requirements
Create a test file `check-requirements.php`:
```php
<?php
echo "PHP Version: " . PHP_VERSION . "\n";
echo "ZipArchive Available: " . (class_exists('ZipArchive') ? 'Yes' : 'No') . "\n";
echo "Upload Max Size: " . ini_get('upload_max_filesize') . "\n";
echo "Post Max Size: " . ini_get('post_max_size') . "\n";
echo "Memory Limit: " . ini_get('memory_limit') . "\n";
?>
```

### Step 2: Check File Permissions
Ensure these directories are writable:
- `/wp-content/uploads/` (755 or 775)
- `/wp-content/uploads/html-apps/` (created automatically)

### Step 3: Check Database
Verify the plugin table exists and has correct columns:
```sql
DESCRIBE wp_html_apps;
```
Should show columns: id, app_name, subdomain, html_content, css_content, js_content, single_html_content, is_single_file, visit_count, app_type, file_path, main_file, created_at

### Step 4: Test File Upload
1. Try uploading a simple ZIP file with just an index.html
2. Check if files are extracted to `/wp-content/uploads/html-apps/app_[ID]/`
3. Verify database entry has correct file_path and main_file values

## Getting Help

If issues persist:
1. Enable WordPress debug logging
2. Check error logs for specific error messages
3. Verify all PHP requirements are met
4. Test with a simple HTML app first
5. Check file and directory permissions

## Common File Structure for ZIP Upload
```
my-app.zip
├── index.html          # Main entry point (required)
├── css/
│   └── styles.css
├── js/
│   └── script.js
├── images/
│   └── logo.png
└── data/
    └── config.json
```

The plugin will automatically find index.html as the main file and serve all other assets correctly.
