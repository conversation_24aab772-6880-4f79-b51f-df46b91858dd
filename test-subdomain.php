<?php
/**
 * Test file to check subdomain routing
 * Place this in your WordPress root directory and access via subdomain
 */

echo "<h1>Subdomain Test</h1>";
echo "<p><strong>Host:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Server Name:</strong> " . $_SERVER['SERVER_NAME'] . "</p>";

// Test if WordPress is loaded
if (defined('ABSPATH')) {
    echo "<p>✅ WordPress is loaded</p>";
    
    // Test database connection
    global $wpdb;
    if ($wpdb) {
        echo "<p>✅ Database connection available</p>";
        
        // Test if our table exists
        $table_name = $wpdb->prefix . 'html_apps';
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
        
        if ($table_exists) {
            echo "<p>✅ HTML Apps table exists</p>";
            
            // Get subdomain
            $host = $_SERVER['HTTP_HOST'];
            $main_host = parse_url(home_url(), PHP_URL_HOST);
            $subdomain = str_replace(['www.', '.' . $main_host], '', $host);
            
            echo "<p><strong>Detected subdomain:</strong> '$subdomain'</p>";
            
            // Check if app exists
            $app = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE subdomain = %s", $subdomain));
            
            if ($app) {
                echo "<p>✅ App found: {$app->app_name} (Type: {$app->app_type})</p>";
                if ($app->app_type === 'files') {
                    echo "<p><strong>File path:</strong> {$app->file_path}</p>";
                    echo "<p><strong>Main file:</strong> {$app->main_file}</p>";
                    
                    // Check if files exist
                    $upload_dir = wp_upload_dir();
                    $app_dir = $upload_dir['basedir'] . '/html-apps/' . $app->file_path;
                    
                    if (file_exists($app_dir)) {
                        echo "<p>✅ App directory exists: $app_dir</p>";
                        
                        $main_file_path = $app_dir . '/' . $app->main_file;
                        if (file_exists($main_file_path)) {
                            echo "<p>✅ Main file exists: $main_file_path</p>";
                        } else {
                            echo "<p>❌ Main file not found: $main_file_path</p>";
                        }
                        
                        // List all files in directory
                        echo "<h3>Files in app directory:</h3><ul>";
                        $files = scandir($app_dir);
                        foreach ($files as $file) {
                            if ($file !== '.' && $file !== '..') {
                                $file_path = $app_dir . '/' . $file;
                                $type = is_dir($file_path) ? 'DIR' : 'FILE';
                                echo "<li>$type: $file</li>";
                            }
                        }
                        echo "</ul>";
                        
                    } else {
                        echo "<p>❌ App directory not found: $app_dir</p>";
                    }
                }
            } else {
                echo "<p>❌ No app found for subdomain '$subdomain'</p>";
                
                // List all apps
                $apps = $wpdb->get_results("SELECT subdomain, app_name, app_type FROM {$table_name}");
                echo "<h3>Available apps:</h3><ul>";
                foreach ($apps as $app) {
                    echo "<li>{$app->subdomain} - {$app->app_name} ({$app->app_type})</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p>❌ HTML Apps table not found</p>";
        }
    } else {
        echo "<p>❌ Database connection not available</p>";
    }
} else {
    echo "<p>❌ WordPress not loaded</p>";
}
?>
