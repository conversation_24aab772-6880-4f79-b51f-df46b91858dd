# Example .htaccess rules for subdomain support
# Add these rules to your main WordPress .htaccess file

# Handle subdomain requests
RewriteEngine On

# Redirect all subdomain requests to WordPress
RewriteCond %{HTTP_HOST} ^([^.]+)\.([^.]+\.[^.]+)$ [NC]
RewriteCond %{HTTP_HOST} !^www\. [NC]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ /index.php [L,QSA]

# Alternative approach - if the above doesn't work, try this:
# RewriteCond %{HTTP_HOST} ^([^.]+)\.yourdomain\.com$ [NC]
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^(.*)$ /index.php [L,QSA]

# Ensure WordPress handles all requests
# BEGIN WordPress
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteBase /
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.php [L]
</IfModule>
# END WordPress
