<?php
/**
 * Debug HTML output for anagram app
 * Access via: http://anagram.jermesa.com/debug-html.php
 */

// Load WordPress
require_once('wp-config.php');

echo "<h1>HTML Debug Output</h1>";

// Get the app
global $wpdb;
$table_name = $wpdb->prefix . 'html_apps';
$app = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE subdomain = %s", 'anagram'));

if ($app) {
    echo "<h2>App Info</h2>";
    echo "<p><strong>App Name:</strong> {$app->app_name}</p>";
    echo "<p><strong>Main File:</strong> {$app->main_file}</p>";
    echo "<p><strong>File Path:</strong> {$app->file_path}</p>";
    
    $upload_dir = wp_upload_dir();
    $app_dir = $upload_dir['basedir'] . '/html-apps/' . $app->file_path;
    $main_file_path = $app_dir . '/' . $app->main_file;
    
    echo "<p><strong>Full Path:</strong> $main_file_path</p>";
    
    if (file_exists($main_file_path)) {
        echo "<h2>Original HTML Content</h2>";
        $original_content = file_get_contents($main_file_path);
        echo "<textarea style='width:100%; height:200px;'>" . htmlspecialchars($original_content) . "</textarea>";
        
        echo "<h2>Processed HTML Content</h2>";
        
        // Simulate the processing
        $main_file_dir = dirname($app->main_file);
        echo "<p><strong>Main file directory:</strong> '$main_file_dir'</p>";
        
        if ($main_file_dir && $main_file_dir !== '.') {
            $host = $_SERVER['HTTP_HOST'];
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $base_url = "{$protocol}://{$host}/{$main_file_dir}/";
            echo "<p><strong>Base URL would be:</strong> $base_url</p>";
            
            // Process the content
            $processed_content = $original_content;
            
            // Add base tag
            $base_tag = "<base href=\"{$base_url}\">";
            if (!preg_match('/<base\s+[^>]*>/i', $processed_content)) {
                if (preg_match('/<head[^>]*>/i', $processed_content)) {
                    $processed_content = preg_replace('/(<head[^>]*>)/i', '$1' . "\n    " . $base_tag, $processed_content, 1);
                } else if (preg_match('/<html[^>]*>/i', $processed_content)) {
                    $processed_content = preg_replace('/(<html[^>]*>)/i', '$1' . "\n<head>\n    " . $base_tag . "\n</head>", $processed_content, 1);
                } else {
                    $processed_content = "<head>\n    " . $base_tag . "\n</head>\n" . $processed_content;
                }
            }
            
            // Fix relative paths
            $processed_content = preg_replace(
                '/(<link[^>]+href=")([^"\/][^"]*\.css)(")/i',
                '$1' . $main_file_dir . '/$2$3',
                $processed_content
            );
            
            $processed_content = preg_replace(
                '/(<script[^>]+src=")([^"\/][^"]*\.js)(")/i',
                '$1' . $main_file_dir . '/$2$3',
                $processed_content
            );
            
            echo "<textarea style='width:100%; height:300px;'>" . htmlspecialchars($processed_content) . "</textarea>";
            
            echo "<h2>Key Differences</h2>";
            echo "<h3>CSS Links Found:</h3>";
            preg_match_all('/<link[^>]+href="([^"]*\.css)"/i', $original_content, $css_matches);
            if ($css_matches[1]) {
                foreach ($css_matches[1] as $css_file) {
                    echo "<p>Original: <code>$css_file</code></p>";
                }
            }
            
            preg_match_all('/<link[^>]+href="([^"]*\.css)"/i', $processed_content, $css_matches_processed);
            if ($css_matches_processed[1]) {
                foreach ($css_matches_processed[1] as $css_file) {
                    echo "<p>Processed: <code>$css_file</code></p>";
                }
            }
            
            echo "<h3>JS Scripts Found:</h3>";
            preg_match_all('/<script[^>]+src="([^"]*\.js)"/i', $original_content, $js_matches);
            if ($js_matches[1]) {
                foreach ($js_matches[1] as $js_file) {
                    echo "<p>Original: <code>$js_file</code></p>";
                }
            }
            
            preg_match_all('/<script[^>]+src="([^"]*\.js)"/i', $processed_content, $js_matches_processed);
            if ($js_matches_processed[1]) {
                foreach ($js_matches_processed[1] as $js_file) {
                    echo "<p>Processed: <code>$js_file</code></p>";
                }
            }
        } else {
            echo "<p>Main file is in root directory, no processing needed.</p>";
        }
    } else {
        echo "<p>❌ Main file not found!</p>";
    }
} else {
    echo "<p>❌ App not found!</p>";
}
?>
