<?php
/**
 * Plugin Name: HTML App Manager
 * Description: Host multiple single-page HTML apps with custom subdomains
 * Version: 3.8
 * Author: Jermesa Studio
 * Network: true
 */

defined('ABSPATH') or die('Direct access not allowed');

class HTML_App_Manager {
    private $table_name;

    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'html_apps';

        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        add_action('admin_menu', [$this, 'add_admin_menu']);
        add_action('admin_init', [$this, 'register_settings']);
        add_action('admin_init', [$this, 'maybe_update_db_schema']);
        add_action('template_redirect', [$this, 'handle_subdomain_request']);

        add_action('wp_ajax_get_html_app', [$this, 'handle_ajax']);
        add_action('wp_ajax_delete_html_app', [$this, 'handle_ajax']);
    }

    /**
     * Check if database schema needs to be updated and update it if necessary
     */
    public function maybe_update_db_schema() {
        global $wpdb;

        // Check if the table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") === $this->table_name;

        if (!$table_exists) {
            // If table doesn't exist, create it
            $this->activate();
            return;
        }

        // Check if single_html_content column exists
        $single_html_content_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'single_html_content'");

        // Check if is_single_file column exists
        $is_single_file_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'is_single_file'");

        // Check if visit_count column exists
        $visit_count_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'visit_count'");

        // If columns don't exist, add them
        if (!$single_html_content_exists || !$is_single_file_exists || !$visit_count_exists) {
            if (!$single_html_content_exists) {
                $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN single_html_content longtext AFTER js_content");
            }

            if (!$is_single_file_exists) {
                $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN is_single_file tinyint(1) DEFAULT 0 AFTER single_html_content");
            }

            if (!$visit_count_exists) {
                $wpdb->query("ALTER TABLE {$this->table_name} ADD COLUMN visit_count int(11) DEFAULT 0 AFTER is_single_file");
            }
        }
    }

    public function activate() {
        global $wpdb;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE {$this->table_name} (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            app_name varchar(100) NOT NULL,
            subdomain varchar(100) NOT NULL,
            html_content longtext NOT NULL,
            css_content longtext,
            js_content longtext,
            single_html_content longtext,
            is_single_file tinyint(1) DEFAULT 0,
            visit_count int(11) DEFAULT 0,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            UNIQUE KEY subdomain (subdomain)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    public function deactivate() {
        // Cleanup if needed
    }

    public function add_admin_menu() {
        add_menu_page(
            'HTML Apps Manager',
            'HTML Apps',
            'manage_options',
            'html-app-manager',
            [$this, 'render_admin_page'],
            'dashicons-editor-code',
            30
        );
    }

    public function register_settings() {
        register_setting('html_app_manager_settings', 'html_app_manager_options');
    }

    private function save_app() {
        if (!isset($_POST['html_app_nonce']) || !wp_verify_nonce($_POST['html_app_nonce'], 'save_html_app')) {
            wp_die('Security check failed');
        }

        // Validate required fields
        if (empty($_POST['app_name']) || empty($_POST['subdomain'])) {
            wp_die('App name and subdomain are required');
        }

        global $wpdb;

        // Make sure the database schema is up to date
        $this->maybe_update_db_schema();

        $is_single_file = isset($_POST['app_type']) && $_POST['app_type'] === 'single_file';

        // Validate content based on app type
        if ($is_single_file && empty($_POST['single_html_content'])) {
            wp_die('Single HTML file content is required');
        } elseif (!$is_single_file && empty($_POST['html_content'])) {
            wp_die('HTML content is required');
        }

        // Basic data that all apps need
        $data = [
            'app_name' => sanitize_text_field($_POST['app_name']),
            'subdomain' => sanitize_title($_POST['subdomain']),
        ];

        // Check if the columns exist before adding them to the data array
        $is_single_file_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'is_single_file'");
        $single_html_content_exists = $wpdb->get_var("SHOW COLUMNS FROM {$this->table_name} LIKE 'single_html_content'");

        if ($is_single_file_exists) {
            $data['is_single_file'] = $is_single_file ? 1 : 0;
        }

        if ($is_single_file) {
            // For single file, we need to be more permissive with HTML tags
            if ($single_html_content_exists) {
                $data['single_html_content'] = stripslashes($_POST['single_html_content']);
            }
            // Set empty values for the separate fields
            $data['html_content'] = '';
            $data['css_content'] = '';
            $data['js_content'] = '';
        } else {
            $data['html_content'] = stripslashes($_POST['html_content']);
            $data['css_content'] = stripslashes($_POST['css_content']);
            $data['js_content'] = stripslashes($_POST['js_content']);
            if ($single_html_content_exists) {
                $data['single_html_content'] = '';
            }
        }

        // Check if the subdomain is already in use (except for the current app being edited)
        $subdomain_check_query = "SELECT id FROM {$this->table_name} WHERE subdomain = %s";
        $subdomain_check_params = [$data['subdomain']];

        if (!empty($_POST['app_id'])) {
            $subdomain_check_query .= " AND id != %d";
            $subdomain_check_params[] = intval($_POST['app_id']);
        }

        $existing_app = $wpdb->get_var($wpdb->prepare($subdomain_check_query, $subdomain_check_params));

        if ($existing_app) {
            wp_die('This subdomain is already in use. Please choose a different subdomain.');
        }

        // Save the app
        if (!empty($_POST['app_id'])) {
            $result = $wpdb->update(
                $this->table_name,
                $data,
                ['id' => intval($_POST['app_id'])]
            );
        } else {
            $result = $wpdb->insert($this->table_name, $data);
        }

        if ($result === false) {
            wp_die('Error saving app: ' . $wpdb->last_error);
        }

        wp_redirect(admin_url('admin.php?page=html-app-manager'));
        exit;
    }

    public function render_admin_page() {
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_app'])) {
            $this->save_app();
        }

        // Get all apps
        global $wpdb;
        $apps = $wpdb->get_results("SELECT * FROM {$this->table_name} ORDER BY created_at DESC");
        ?>
        <div class="wrap">
            <h1>HTML App Manager</h1>

            <div class="card" style="width: 1200px;">
                <h2>Your Apps</h2>
                <?php if ($apps) : ?>
                    <div class="apps-table-container">
                        <div class="table-responsive">
                            <table class="wp-list-table widefat" style="width: 100%;">
                                <thead>
                                    <tr>
                                        <th>App Name</th>
                                        <th>Subdomain</th>
                                        <th>Type</th>
                                        <th>Visits</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($apps as $app) : ?>
                                        <tr>
                                            <td><?php echo esc_html($app->app_name); ?></td>
                                            <td>
                                                <a href="http://<?php echo esc_html($app->subdomain); ?>.<?php echo parse_url(network_site_url(), PHP_URL_HOST); ?>" target="_blank">
                                                    <?php echo esc_html($app->subdomain); ?>.<?php echo parse_url(network_site_url(), PHP_URL_HOST); ?>
                                                </a>
                                            </td>
                                            <td><?php echo (property_exists($app, 'is_single_file') && $app->is_single_file) ? 'Single HTML File' : 'Separate HTML/CSS/JS'; ?></td>
                                            <td><?php echo esc_html($app->visit_count); ?></td>
                                            <td><?php echo date('M j, Y', strtotime($app->created_at)); ?></td>
                                            <td class="actions-column">
                                                <a href="#" class="button edit-app" data-id="<?php echo $app->id; ?>">Edit</a>
                                                <a href="#" class="button button-danger delete-app" data-id="<?php echo $app->id; ?>">Delete</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php else : ?>
                    <div class="no-apps-message">
                        <p>No apps found. Add your first app below.</p>
                    </div>
                <?php endif; ?>
            </div>

            <div class="card">
                <h2 id="form-title">Add New App</h2>
                <form method="post" id="app-form">
                    <input type="hidden" name="app_id" id="app_id" value="">
                    <?php wp_nonce_field('save_html_app', 'html_app_nonce'); ?>
                    <div class="form-fields">
                        <div class="form-field">
                            <label for="app_name">App Name</label>
                            <input type="text" name="app_name" required class="regular-text">
                        </div>

                        <div class="form-field">
                            <label for="subdomain">Subdomain</label>
                            <input type="text" name="subdomain" required class="regular-text">
                            <p class="description">e.g. "calendar" for calendar.yoursite.com</p>
                        </div>

                        <div class="form-field">
                            <label for="app_type">App Type</label>
                            <select name="app_type" id="app_type">
                                <option value="separate_files">Separate HTML/CSS/JS</option>
                                <option value="single_file">Single HTML File</option>
                            </select>
                            <p class="description">Choose how you want to add your app content</p>
                        </div>

                        <!-- Separate HTML/CSS/JS fields -->
                        <div class="separate-files-fields">
                            <div class="form-field">
                                <label for="html_content">HTML Content</label>
                                <textarea name="html_content" id="html_content" rows="10" class="large-text code"></textarea>
                                <p class="description">Required for separate HTML/CSS/JS option</p>
                            </div>

                            <div class="form-field">
                                <label for="css_content">CSS</label>
                                <textarea name="css_content" id="css_content" rows="10" class="large-text code"></textarea>
                            </div>

                            <div class="form-field">
                                <label for="js_content">JavaScript</label>
                                <textarea name="js_content" id="js_content" rows="10" class="large-text code"></textarea>
                            </div>
                        </div>

                        <!-- Single HTML file field -->
                        <div class="single-file-fields" style="display: none;">
                            <div class="form-field">
                                <label for="single_html_content">Complete HTML File</label>
                                <textarea name="single_html_content" id="single_html_content" rows="20" class="large-text code"></textarea>
                                <p class="description">Paste your complete HTML file including all HTML, CSS, and JavaScript</p>
                            </div>
                        </div>

                        <div class="form-field submit-field">
                            <?php submit_button('Save App', 'primary', 'save_app', false); ?>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <style>
            /* Modern UI Styles */
            :root {
                --primary-color: #2271b1;
                --secondary-color: #f0f0f1;
                --border-color: #dcdcde;
                --text-color: #1d2327;
                --danger-color: #d63638;
                --success-color: #00a32a;
                --card-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
                --transition: all 0.3s ease;
            }

            .wrap h1 {
                margin-bottom: 20px;
                font-weight: 500;
            }

            .card {
                background: #fff;
                padding: 25px;
                margin-bottom: 25px;
                border-radius: 4px;
                box-shadow: var(--card-shadow);
                border: 1px solid var(--border-color);
                max-width: 1000px;
            }

            .card h2 {
                margin-top: 0;
                margin-bottom: 20px;
                font-weight: 500;
                color: var(--text-color);
                border-bottom: 1px solid var(--border-color);
                padding-bottom: 10px;
            }

           /* Table styles */
            .wp-list-table {
                border-collapse: collapse;
                width: 100%;
                border: none;
                box-shadow: none;
            }

            .wp-list-table th {
                text-align: left;
                padding: 12px 10px;
                font-weight: 500;
                color: var(--text-color);
                border-bottom: 1px solid var(--border-color);
            }

            .wp-list-table td {
                padding: 12px 10px;
                vertical-align: middle;
                border-bottom: 1px solid var(--border-color);
            }

            /* Form styles */
            .form-fields {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .form-field {
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .form-field label {
                font-weight: 500;
                color: var(--text-color);
            }

            .form-field input[type="text"],
            .form-field select,
            .form-field textarea {
                padding: 8px 12px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                transition: var(--transition);
            }

            .form-field input[type="text"]:focus,
            .form-field select:focus,
            .form-field textarea:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 1px var(--primary-color);
                outline: none;
            }

            .description {
                font-size: 13px;
                color: #666;
                margin-top: 4px;
            }

            .large-text.code {
                font-family: monospace;
                white-space: pre;
                min-height: 150px;
            }

            /* Button styles */
            .button {
                display: inline-block;
                padding: 6px 12px;
                background: #f6f7f7;
                border: 1px solid #dcdcde;
                border-radius: 3px;
                cursor: pointer;
                font-size: 13px;
                text-decoration: none;
                transition: var(--transition);
                margin-right: 5px;
            }

            .button:hover {
                background: #f0f0f1;
            }

            .button-primary {
                background: var(--primary-color);
                border-color: var(--primary-color);
                color: white;
            }

            .button-primary:hover {
                background: #135e96;
                border-color: #135e96;
            }

            .button-danger {
                color: var(--danger-color);
                border-color: var(--danger-color);
            }

            .button-danger:hover {
                background: #f6e1e1;
            }

            .submit-field {
                margin-top: 10px;
            }

            /* Table container */
            .apps-table-container {
                overflow-x: auto;
                margin-bottom: 15px;
                width: 100%;
            }

            /* No apps message */
            .no-apps-message {
                padding: 20px;
                background: var(--secondary-color);
                border-radius: 4px;
                text-align: center;
                color: #666;
            }

            /* Actions column */
            .actions-column {
                white-space: nowrap;
            }

            /* Responsive adjustments */
            @media (max-width: 782px) {
                .form-field {
                    margin-bottom: 15px;
                }

                .wp-list-table th,
                .wp-list-table td {
                    padding: 10px 8px;
                }

                .button {
                    padding: 5px 10px;
                    font-size: 12px;
                }
            }
        </style>
        <script>
        jQuery(document).ready(function($) {
            // Function to toggle between separate files and single file
            function toggleAppType(selectedType) {
                if (selectedType === 'single_file') {
                    $('.separate-files-fields').hide();
                    $('.single-file-fields').show();
                } else {
                    $('.separate-files-fields').show();
                    $('.single-file-fields').hide();
                }
            }

            // Run toggle on page load
            toggleAppType($('#app_type').val());

            // Run toggle on change
            $('#app_type').change(function() {
                toggleAppType($(this).val());
            });

            // Form validation before submit
            $('#app-form').on('submit', function(e) {
                var appType = $('#app_type').val();
                var isValid = true;

                // Clear any previous error messages
                $('.validation-error').remove();

                if (appType === 'single_file') {
                    if (!$('#single_html_content').val().trim()) {
                        e.preventDefault();
                        $('#single_html_content').after('<p class="validation-error" style="color: red;">Please enter the complete HTML file content</p>');
                        $('#single_html_content').focus();
                        isValid = false;
                    }
                } else {
                    if (!$('#html_content').val().trim()) {
                        e.preventDefault();
                        $('#html_content').after('<p class="validation-error" style="color: red;">Please enter the HTML content</p>');
                        $('#html_content').focus();
                        isValid = false;
                    }
                }

                if (!isValid) {
                    return false;
                }

                // If we get here, the form is valid
                return true;
            });

            // Handle edit click
            $('.edit-app').click(function(e) {
                e.preventDefault();
                var appId = $(this).data('id');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_html_app',
                        id: appId,
                        _wpnonce: '<?php echo wp_create_nonce("html_app_action"); ?>'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#form-title').text('Edit App');
                            $('#app_id').val(response.data.id);
                            $('input[name="app_name"]').val(response.data.app_name);
                            $('input[name="subdomain"]').val(response.data.subdomain);

                            // Set the app type and show/hide appropriate fields
                            if (response.data.is_single_file) {
                                $('#app_type').val('single_file');
                                $('#single_html_content').val(response.data.single_html_content);
                            } else {
                                $('#app_type').val('separate_files');
                                $('#html_content').val(response.data.html_content);
                                $('#css_content').val(response.data.css_content);
                                $('#js_content').val(response.data.js_content);
                            }

                            // Toggle fields based on app type
                            toggleAppType($('#app_type').val());

                            // Scroll to form
                            $('html, body').animate({
                                scrollTop: $('#app-form').offset().top - 50
                            }, 500);
                        }
                    }
                });
            });

            // Handle delete click
            $('.delete-app').click(function(e) {
                e.preventDefault();
                if (!confirm('Are you sure you want to delete this app?')) {
                    return;
                }

                var appId = $(this).data('id');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'delete_html_app',
                        id: appId,
                        _wpnonce: '<?php echo wp_create_nonce("html_app_action"); ?>'
                    },
                    success: function() {
                        location.reload();
                    }
                });
            });
        });
        </script>
        <?php
    }

    public function handle_ajax() {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'html_app_action')) {
            wp_send_json_error('Invalid nonce');
        }

        global $wpdb;

        if ($_POST['action'] === 'get_html_app') {
            $app = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                intval($_POST['id'])
            ));

            if ($app) {
                // Decode HTML entities in JavaScript content
                $app->js_content = html_entity_decode($app->js_content);

                // Check if is_single_file property exists
                if (property_exists($app, 'is_single_file')) {
                    // Convert is_single_file to boolean for JavaScript
                    $app->is_single_file = (bool)$app->is_single_file;
                } else {
                    // Default to false if property doesn't exist
                    $app->is_single_file = false;
                }

                // Ensure single_html_content property exists
                if (!property_exists($app, 'single_html_content')) {
                    $app->single_html_content = '';
                }

                wp_send_json_success($app);
            }
            wp_send_json_error('App not found');
        }
        elseif ($_POST['action'] === 'delete_html_app') {
            $wpdb->delete($this->table_name, ['id' => intval($_POST['id'])]);
            wp_send_json_success();
        }
    }

    public function handle_subdomain_request() {
        $host = $_SERVER['HTTP_HOST'];
        $subdomain = str_replace('.' . parse_url(network_site_url(), PHP_URL_HOST), '', $host);

        global $wpdb;
        $app = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE subdomain = %s", $subdomain)
        );

        if ($app) {
            // Increment visit count
            global $wpdb;
            $wpdb->query(
                $wpdb->prepare(
                    "UPDATE {$this->table_name} SET visit_count = visit_count + 1 WHERE id = %d",
                    $app->id
                )
            );

            $this->render_app($app);
            exit;
        }
    }

    private function render_app($app) {
        // Check if this is a single file app (check if property exists first)
        if (property_exists($app, 'is_single_file') &&
            property_exists($app, 'single_html_content') &&
            $app->is_single_file &&
            !empty($app->single_html_content)) {
            // Output the single HTML file content directly
            echo $app->single_html_content;
            return;
        }

        // Otherwise render the traditional way with separate HTML/CSS/JS
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo esc_html($app->app_name); ?></title>
    <style><?php echo $app->css_content; ?></style>
</head>
<body>
    <?php echo $app->html_content; ?>
    <?php if (!empty($app->js_content)) : ?>
        <script>
            <?php echo html_entity_decode($app->js_content); ?>
        </script>
    <?php endif; ?>
</body>
</html>
<?php
    }
}

new HTML_App_Manager();
