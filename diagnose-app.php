<?php
/**
 * Diagnostic script for HTML App Manager
 * Place this in your WordPress root and access via: http://anagram.jermesa.com/diagnose-app.php
 */

// Load WordPress
require_once('wp-config.php');

echo "<h1>HTML App Manager Diagnostics</h1>";

// Basic server info
echo "<h2>Server Information</h2>";
echo "<p><strong>Host:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Request URI:</strong> " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p><strong>Document Root:</strong> " . $_SERVER['DOCUMENT_ROOT'] . "</p>";

// WordPress info
echo "<h2>WordPress Information</h2>";
echo "<p><strong>WordPress loaded:</strong> " . (defined('ABSPATH') ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Home URL:</strong> " . home_url() . "</p>";
echo "<p><strong>Site URL:</strong> " . site_url() . "</p>";

// Database connection
global $wpdb;
$table_name = $wpdb->prefix . 'html_apps';

echo "<h2>Database Information</h2>";
echo "<p><strong>Database connected:</strong> " . ($wpdb ? '✅ Yes' : '❌ No') . "</p>";

$table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$table_name}'") === $table_name;
echo "<p><strong>HTML Apps table exists:</strong> " . ($table_exists ? '✅ Yes' : '❌ No') . "</p>";

if ($table_exists) {
    $app_count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
    echo "<p><strong>Total apps:</strong> $app_count</p>";
}

// Subdomain detection
echo "<h2>Subdomain Detection</h2>";
$host = $_SERVER['HTTP_HOST'];
$main_host = parse_url(home_url(), PHP_URL_HOST);
$subdomain = str_replace(['www.', '.' . $main_host], '', $host);

echo "<p><strong>Current host:</strong> $host</p>";
echo "<p><strong>Main host:</strong> $main_host</p>";
echo "<p><strong>Detected subdomain:</strong> '$subdomain'</p>";

$is_subdomain = ($subdomain !== $main_host && $subdomain !== 'www' && !empty($subdomain) && $subdomain !== $host);
echo "<p><strong>Is subdomain request:</strong> " . ($is_subdomain ? '✅ Yes' : '❌ No') . "</p>";

// App lookup
if ($is_subdomain && $table_exists) {
    echo "<h2>App Lookup</h2>";
    $app = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE subdomain = %s", $subdomain));
    
    if ($app) {
        echo "<p>✅ <strong>App found:</strong> {$app->app_name}</p>";
        echo "<p><strong>App Type:</strong> {$app->app_type}</p>";
        echo "<p><strong>App ID:</strong> {$app->id}</p>";
        
        if ($app->app_type === 'files') {
            echo "<p><strong>File Path:</strong> {$app->file_path}</p>";
            echo "<p><strong>Main File:</strong> {$app->main_file}</p>";
            
            // Check file system
            echo "<h3>File System Check</h3>";
            $upload_dir = wp_upload_dir();
            $app_dir = $upload_dir['basedir'] . '/html-apps/' . $app->file_path;
            
            echo "<p><strong>Upload dir:</strong> {$upload_dir['basedir']}</p>";
            echo "<p><strong>App directory:</strong> $app_dir</p>";
            echo "<p><strong>App dir exists:</strong> " . (file_exists($app_dir) ? '✅ Yes' : '❌ No') . "</p>";
            
            if (file_exists($app_dir)) {
                $main_file_path = $app_dir . '/' . $app->main_file;
                echo "<p><strong>Main file path:</strong> $main_file_path</p>";
                echo "<p><strong>Main file exists:</strong> " . (file_exists($main_file_path) ? '✅ Yes' : '❌ No') . "</p>";
                
                // List files
                echo "<h4>Files in app directory:</h4>";
                echo "<ul>";
                $files = scandir($app_dir);
                foreach ($files as $file) {
                    if ($file !== '.' && $file !== '..') {
                        $file_path = $app_dir . '/' . $file;
                        $type = is_dir($file_path) ? 'DIR' : 'FILE';
                        $size = is_file($file_path) ? ' (' . filesize($file_path) . ' bytes)' : '';
                        echo "<li>$type: $file$size</li>";
                    }
                }
                echo "</ul>";
                
                // Test asset URLs
                echo "<h4>Asset URL Tests</h4>";
                $test_files = ['styles.css', 'script.js', 'admin-styles.css', 'admin-script.js'];
                foreach ($test_files as $test_file) {
                    $test_path = $app_dir . '/' . $test_file;
                    if (file_exists($test_path)) {
                        $test_url = "http://{$subdomain}.{$main_host}/{$test_file}";
                        echo "<p>📄 <a href='$test_url' target='_blank'>$test_file</a> - " . filesize($test_path) . " bytes</p>";
                    }
                }
            }
        }
    } else {
        echo "<p>❌ <strong>No app found for subdomain:</strong> '$subdomain'</p>";
        
        // Show available apps
        $apps = $wpdb->get_results("SELECT subdomain, app_name, app_type FROM {$table_name}");
        if ($apps) {
            echo "<h3>Available apps:</h3><ul>";
            foreach ($apps as $available_app) {
                echo "<li><strong>{$available_app->subdomain}</strong> - {$available_app->app_name} ({$available_app->app_type})</li>";
            }
            echo "</ul>";
        }
    }
}

// Plugin status
echo "<h2>Plugin Status</h2>";
$active_plugins = get_option('active_plugins');
$plugin_active = in_array('html-app-manager/html-app-manager.php', $active_plugins);
echo "<p><strong>Plugin active:</strong> " . ($plugin_active ? '✅ Yes' : '❌ No') . "</p>";

// Server configuration
echo "<h2>Server Configuration</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>ZipArchive available:</strong> " . (class_exists('ZipArchive') ? '✅ Yes' : '❌ No') . "</p>";
echo "<p><strong>Upload max filesize:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>Post max size:</strong> " . ini_get('post_max_size') . "</p>";

// .htaccess check
echo "<h2>.htaccess Check</h2>";
$htaccess_path = ABSPATH . '.htaccess';
echo "<p><strong>.htaccess exists:</strong> " . (file_exists($htaccess_path) ? '✅ Yes' : '❌ No') . "</p>";

if (file_exists($htaccess_path)) {
    $htaccess_content = file_get_contents($htaccess_path);
    $has_rewrite = strpos($htaccess_content, 'RewriteEngine On') !== false;
    echo "<p><strong>RewriteEngine enabled:</strong> " . ($has_rewrite ? '✅ Yes' : '❌ No') . "</p>";
}

echo "<hr>";
echo "<p><em>If you see issues above, check the troubleshooting guide for solutions.</em></p>";
?>
